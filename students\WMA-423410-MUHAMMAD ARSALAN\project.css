/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Top Header */
.top-header {
    background-color: #f85606;
    padding: 8px 0;
    font-size: 12px;
    position: sticky;
    
}

.parent {
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    grid-template-rows: repeat(10, 1fr);
    gap: 8px;
    width: 1100px;
    height: 900px;
}
    
.div1 {
    grid-column: span 7 / span 7;
    grid-row: span 5 / span 5;
   
}

.div2 {
    grid-column: span 9 / span 9;
    grid-row: span 2 / span 2;
    grid-row-start: 6;
}
  .div3 {
    grid-column: span 3 / span 3;
    grid-row: span 5 / span 5;
    grid-column-start: 9;
}      
.top-nav {
    gap: 20px;
}

.top-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.top-link:hover {
    opacity: 0.8;
    color: white;
}

.urdu-text {
    font-family: 'Noto Nastaliq Urdu', serif;
}

/* Main Header */
.main-header {
    background-color: #f85606;
    padding: 15px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
}

.logo img {
    height: 40px;
}

.search-container {
    max-width: 600px;
    margin: 0 auto;
}

.search-input {
    border: none;
    border-radius: 2px 0 0 2px;
    padding: 12px 15px;
    font-size: 14px;
    box-shadow: none;
}

.search-input:focus {
    outline: none;
    box-shadow: none;
    border-color: #ddd;
}

.search-btn {
    background-color:aliceblue;
    border: none;
    border-radius: 0 2px 2px 0;
    padding: 12px 20px;
    color: #ff9800;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background-color: #ff9800;
}

.cart-link {
    color: white;
    font-size: 24px;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.cart-link:hover {
    transform: scale(1.1);
    color: white;
}

/* Main Banner */
.main-banner {
    background: linear-gradient(135deg, #f85606 0%, #ff7043 100%);
    padding: 20px 0;
    min-height: 400px;
}

.banner-slider {
    position: relative;
    height: 350px;
    overflow: hidden;
    border-radius: 8px;
}

.banner-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.banner-slide.active {
    opacity: 1;
}

.banner-slide:first-child {
    background: linear-gradient(135deg, #00897b 0%, #4db6ac 100%);
}

.banner-slide:nth-child(2) {
    background: linear-gradient(135deg, #00bcd4 0%, #4dd0e1 100%);
}

.banner-content {
    height: 100%;
    padding: 40px;
    display: flex;
    align-items: center;
}

.banner-title {
    font-size: 3.5rem;
    font-weight: 900;
    color: white;
    line-height: 1;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: slideInLeft 1s ease-out;
}

.banner-subtitle {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 30px;
    animation: slideInLeft 1s ease-out 0.2s both;
}

.shop-now-btn {
    background-color: #ffeb3b;
    color: #333;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
    animation: slideInLeft 1s ease-out 0.4s both;
}
.shop-now-btn:hover {
    background-color: #fdd835;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.shop-now-btn-white {
    background-color: white;
    color: #333;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
}

.shop-now-btn-white:hover {
    background-color: #f5f5f5;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.discount-badge {
    background-color: #ffeb3b;
    border-radius: 50%;
    width: 120px;
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 20px;
    right: 20px;
    
}

.up-to {
    font-size: 12px;
    font-weight: bold;
    color: #333;
}

.percentage {
    font-size: 28px;
    font-weight: 900;
    color: #f44336;
    line-height: 1;
}

.off {
    font-size: 12px;
    font-weight: bold;
    color: #333;
}

.product-images {
    display: flex;
    gap: 15px;
    margin-top: 20px;
   
}

.product-img {
    width: 80px;
    height: 100px;
    object-fit: contain;
    background: white;
    border-radius: 8px;
    padding: 5px;
    transition: transform 0.3s ease;
}

.product-img:hover {
    transform: scale(1.1);
}

.back-to-school {
    background: linear-gradient(135deg, #00bcd4 0%, #4dd0e1 100%);
}

.school-items {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.school-item {
    width: 80px;
    height: 80px;
    object-fit: contain;
    
}



.school-title {
    font-size: 3rem;
    font-weight: 900;
    color: white;
    line-height: 1;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.school-highlight {
    color: #ffeb3b;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.banner-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.indicator.active {
    background-color: white;
}

/* App Download Section */
.app-download-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    height: 350px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    animation: slideInRight 1s ease-out;
}

.app-header {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #f85606;
    font-weight: bold;
    font-size: 16px;
}

.app-header i {
    font-size: 20px;
}

.app-rating {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
}

.stars {
    color: #ffa726;
}

.rating-text {
    background-color: #f85606;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.download-text {
    font-weight: bold;
    color: #333;
}

.app-features {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 15px 0;
}

.feature {
    display: flex;
    align-items: center;
    gap: 10px;
}

.feature i {
    color: #4caf50;
    font-size: 18px;
}

.feature-text {
    display: flex;
    flex-direction: column;
}

.feature-title {
    font-weight: bold;
    color: #333;
    font-size: 14px;
}

.feature-desc {
    color: #666;
    font-size: 12px;
}

.qr-code {
    text-align: center;
    margin: 15px 0;
}

.qr-img {
    width: 80px;
    height: 80px;
}

.app-stores {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.store-badge {
    height: 35px;
    object-fit: contain;
}

.download-now {
    text-align: center;
    font-size: 12px;
    color: #666;
    margin-top: 10px;
}

/* Flash Sale Section */
.flash-sale-section {
    background-color: #fff;
    padding: 0;
    border-top: 1px solid #eee;
}

.section-header {
   
    justify-content: space-between;
    align-items: center;
   
}

.section-title {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.section-actions {
   
    align-items: center;
    gap: 20px;
}

.on-sale {
    color: #f85606;
    font-weight: bold;
}

.shop-all-btn {
   
    color: #f85606;
    border: 2px solid #f85606;
    padding: 8px 20px;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.shop-all-btn:hover {
    background-color: #f85606;
    color: white;
}

.products-row {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding-bottom: 10px;
}

.product-card {
    min-width: 200px;
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    animation: fadeInUp 0.6s ease-out;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.product-image {
    width: 100%;
    height: 150px;
    object-fit: contain;
    margin-bottom: 10px;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
    height: 40px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.current-price {
    font-size: 16px;
    font-weight: bold;
    color: #f85606;
}

.original-price {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
}

.discount {
    background-color: #f85606;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.product-rating .stars {
    color: #ffa726;
    font-size: 12px;
}

.rating-count {
    font-size: 12px;
    color: #666;
}

/* Categories Section */
.categories-section {
    background-color: #f8f9fa;
    padding: 40px 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.category-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    animation: fadeInUp 0.6s ease-out;
}

.category-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.category-icon {
    width: 60px;
    height: 60px;
    object-fit: contain;
    margin-bottom: 10px;
    transition: transform 0.3s ease;
}

.category-item:hover .category-icon {
    transform: scale(1.1);
}

.category-name {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    display: block;
}

/* Just For You Section */
.just-for-you-section {
    background-color: #fff;
    padding: 40px 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.load-more-container {
    text-align: center;
    margin-top: 40px;
}

.load-more-btn {
    background-color: transparent;
    color: #f85606;
    border: 2px solid #f85606;
    padding: 12px 40px;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    background-color: #f85606;
    color: white;
}

/* Footer */
.main-footer {
    background-color: #f8f9fa;
    padding: 40px 0 20px;
    border-top: 1px solid #eee;
}

.footer-title {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-links a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #f85606;
}

.happy-shopping {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.happy-icon {
    width: 40px;
    height: 40px;
}

.happy-text {
    display: flex;
    flex-direction: column;
}

.happy-title {
    font-weight: bold;
    color: #333;
}

.download-app {
    font-size: 12px;
    color: #666;
}

.app-badges {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.app-badge {
    height: 35px;
    object-fit: contain;
}

.payment-methods {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.payment-logo {
    height: 30px;
    object-fit: contain;
    background: white;
    padding: 5px;
    border-radius: 4px;
    border: 1px solid #eee;
}

.verification {
    margin-top: 10px;
}

.verification-logo {
    height: 40px;
    object-fit: contain;
}

.footer-description {
    border-top: 1px solid #eee;
    padding-top: 30px;
}

.footer-description h5 {
    color: #333;
    margin-bottom: 15px;
}

.footer-description p {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
}

/* Animations */

/* Responsive Design */
@media (max-width: 768px) {
    .top-nav {
        gap: 10px;
        font-size: 10px;
    }
    
    .banner-title {
        font-size: 2.5rem;
    }
    
    .banner-subtitle {
        font-size: 1.2rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .products-row {
        gap: 15px;
    }
    
    .product-card {
        min-width: 180px;
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 15px;
    }
    
    .app-download-section {
        margin-top: 20px;
    }
    
    .banner-content {
        padding: 20px;
    }
    
    .school-title {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .banner-title {
        font-size: 2rem;
    }
    
    .banner-subtitle {
        font-size: 1rem;
    }
    
    .product-card {
        min-width: 160px;
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }
    
    .main-footer .row > div {
        margin-bottom: 30px;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading animation for images */




/* Focus states for accessibility */
.search-input:focus,
.shop-now-btn:focus,
.load-more-btn:focus,
.shop-all-btn:focus {
    outline: 2px solid #f85606;
    outline-offset: 2px;
}

/* Custom scrollbar for products row */
.products-row::-webkit-scrollbar {
    height: 6px;
}

.products-row::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.products-row::-webkit-scrollbar-thumb {
    background: #f85606;
    border-radius: 3px;
}

.products-row::-webkit-scrollbar-thumb:hover {
    background: #e64a19;
}